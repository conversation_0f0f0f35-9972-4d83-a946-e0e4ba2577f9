# Canvas Management System

This document describes the new canvas management functionality implemented in the Neurino application.

## Overview

The canvas management system allows users to create, manage, and switch between multiple mind map canvases within a single session. Each canvas maintains its own independent state including nodes, connections, viewport position, and zoom level.

## Features

### 1. Multiple Canvas Support
- Create unlimited canvases
- Each canvas has a unique identifier and name
- Independent mind map data for each canvas
- Automatic persistence to localStorage

### 2. Canvas Sidebar
- **Location**: Left side of the interface
- **Collapsible**: Toggle button to expand/collapse for better screen space utilization
- **Canvas List**: Shows all canvases sorted by creation date (oldest first)
- **Canvas Count**: Displays total number of canvases in the header

#### Sidebar Features:
- **New Canvas Button**: Create a new canvas with auto-incremented name
- **Canvas Switching**: Click on any canvas to switch to it
- **Active Canvas Indicator**: Highlighted background for the currently active canvas
- **Last Modified Time**: Shows relative time (e.g., "2h ago", "Just now")
- **Context Menu**: Right-click or click the three-dots menu for additional options

### 3. Canvas Operations

#### Create Canvas
- Click "New Canvas" button in sidebar
- Automatically generates name like "Canvas 1", "Canvas 2", etc.
- Switches to the new canvas immediately
- Creates a fresh mind map with a single root node

#### Rename Canvas
- Click the edit icon in the context menu
- Inline editing with Enter to save, Escape to cancel
- Validation to prevent empty names

#### Duplicate Canvas
- Click the duplicate icon in the context menu
- Creates an exact copy with "(Copy)" suffix
- Includes all nodes, connections, and viewport state
- Switches to the duplicated canvas

#### Delete Canvas
- Click the delete icon in the context menu
- Confirmation dialog to prevent accidental deletion
- Cannot delete the last remaining canvas
- Automatically switches to another canvas if deleting the active one

### 4. Canvas Tabs
- **Location**: Top of the main area
- **Quick Switching**: Click on any tab to switch canvases
- **Close Button**: X button on each tab (when multiple canvases exist)
- **Responsive**: Horizontal scrolling for many canvases
- **Active Indicator**: Highlighted tab for current canvas

### 5. Data Migration
- Automatically migrates existing single-canvas data to new multi-canvas format
- Preserves all existing mind map data
- Seamless upgrade without data loss

## Technical Implementation

### Storage Structure
```typescript
interface CanvasManagerData {
  canvases: Record<string, Canvas>;
  activeCanvasId: string;
  version: number;
}

interface Canvas {
  id: string;
  name: string;
  data: MindMapData;
  viewport: ViewportState;
  createdAt: number;
  lastModified: number;
}
```

### Key Components
- `useCanvasManager`: Hook for canvas management logic
- `CanvasSidebar`: Left sidebar component
- `CanvasTabs`: Top tabs component
- `MindMap`: Updated main component with canvas integration

### Storage Keys
- `canvas-manager-data`: Main canvas data storage
- `mindmap-data`: Legacy key (automatically migrated)

## User Interface

### Layout Changes
- **Sidebar**: 320px width when expanded, 48px when collapsed
- **Main Area**: Adjusted margins to accommodate sidebar
- **Toolbar**: Repositioned to account for sidebar
- **Canvas Tabs**: 48px height at the top
- **Responsive**: Mobile-friendly with overlay behavior

### Visual Indicators
- **Active Canvas**: Blue background in sidebar and tabs
- **Hover Effects**: Subtle hover states for better UX
- **Transitions**: Smooth animations for expand/collapse
- **Icons**: Lucide React icons for consistent design

## Usage Instructions

1. **Creating Canvases**: Click "New Canvas" in the sidebar
2. **Switching Canvases**: Click on canvas name in sidebar or tab at top
3. **Managing Canvases**: Use the three-dots menu for rename, duplicate, delete
4. **Organizing**: Canvases are automatically sorted by creation date (oldest first)
5. **Space Management**: Collapse sidebar when working on complex mind maps

## Keyboard Shortcuts
- The existing keyboard shortcuts work within each canvas independently
- No new keyboard shortcuts added for canvas management

## Data Persistence
- All canvas data is automatically saved to localStorage
- Changes are persisted immediately when switching canvases
- No manual save required
- Data survives browser refresh and restart

## Compatibility
- Fully backward compatible with existing mind maps
- Automatic migration of legacy data
- No breaking changes to existing functionality
- Maintains all existing features within each canvas
