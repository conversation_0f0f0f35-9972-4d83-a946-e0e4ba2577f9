import React, { useState, useEffect } from 'react';
import {
  ZoomIn,
  ZoomOut,
  Download,
  Upload,
  RotateCcw,
  Sun,
  Moon,
  Plus,
  KeyRound
} from 'lucide-react';
import { ViewportState } from '../types';
import { Theme } from '../hooks/useTheme';
import { ConfirmationDialog } from './ConfirmationDialog';
import { ModelSelector } from './ModelSelector';

interface ToolbarProps {
  viewport: ViewportState;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
  onExport: () => void;
  onImport: () => void;
  onNewCanvas: () => void;
  theme: Theme;
  onToggleTheme: () => void;
  apiKey: string;
  onSetApiKey: (key: string) => void;
  selectedModel: string;
  onSetSelectedModel: (model: string) => void;
}

export const Toolbar: React.FC<ToolbarProps> = ({
  viewport,
  onZoomIn,
  onZoomOut,
  onResetView,
  onExport,
  onImport,
  onNewCanvas,
  theme,
  onToggleTheme,
  apiKey,
  onSetApiKey,
  selectedModel,
  onSetSelectedModel,
}) => {
  const [showNewCanvasDialog, setShowNewCanvasDialog] = useState(false);
  const [localApiKey, setLocalApiKey] = useState(apiKey);
  const [isKeySaved, setIsKeySaved] = useState(false);

  useEffect(() => {
    setLocalApiKey(apiKey);
  }, [apiKey]);

  const handleImportClick = () => {
    onImport();
  };

  const handleNewCanvasClick = () => {
    setShowNewCanvasDialog(true);
  };

  const handleConfirmNewCanvas = () => {
    onNewCanvas();
  };

  const handleApiKeySave = () => {
    onSetApiKey(localApiKey);
    setIsKeySaved(true);
    setTimeout(() => setIsKeySaved(false), 2000);
  };

  return (
    <>
      <div className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-2 flex items-center gap-2 shadow-lg">
        {/* Canvas Actions */}
        <div className="flex items-center gap-1">
          <button
            onClick={handleNewCanvasClick}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="New Canvas"
          >
            <Plus size={18} />
          </button>
          <button
            onClick={onExport}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Export Mind Map"
          >
            <Download size={18} />
          </button>
          <button
            onClick={handleImportClick}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Import Mind Map"
          >
            <Upload size={18} />
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* View Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={onZoomOut}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Zoom Out"
          >
            <ZoomOut size={18} />
          </button>
          <span className="text-sm font-mono text-gray-600 dark:text-gray-400 min-w-[4rem] text-center">
            {Math.round(viewport.zoom * 100)}%
          </span>
          <button
            onClick={onZoomIn}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
            title="Zoom In"
          >
            <ZoomIn size={18} />
          </button>
        </div>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        <button
          onClick={onResetView}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title="Reset View"
        >
          <RotateCcw size={18} />
        </button>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        <button
          onClick={onToggleTheme}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700/50 rounded-md transition-colors text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          title={`Switch to ${theme === 'dark' ? 'Light' : 'Dark'} Mode`}
        >
          {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
        </button>

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* Model Selector */}
        <ModelSelector
          selectedModel={selectedModel}
          onModelChange={onSetSelectedModel}
          disabled={!apiKey}
        />

        <div className="w-px h-6 bg-gray-300 dark:bg-gray-700/50" />

        {/* API Key Input */}
        <div className="flex items-center gap-2">
          <KeyRound size={18} className="text-gray-600 dark:text-gray-400" />
          <input
            type="password"
            placeholder="Gemini API Key"
            className="bg-transparent text-sm w-40 focus:outline-none text-gray-700 dark:text-gray-300 placeholder-gray-400 dark:placeholder-gray-500"
            value={localApiKey}
            onChange={(e) => setLocalApiKey(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && handleApiKeySave()}
          />
          <button
            onClick={handleApiKeySave}
            className={`text-sm px-3 py-1 rounded-md transition-colors ${isKeySaved ? 'bg-green-500' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
            disabled={isKeySaved}
          >
            {isKeySaved ? 'Saved' : 'Save'}
          </button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showNewCanvasDialog}
        onClose={() => setShowNewCanvasDialog(false)}
        onConfirm={handleConfirmNewCanvas}
        title="Start New Canvas"
        message="Are you sure you want to start a new canvas? This will permanently delete your current mind map and cannot be undone. Make sure to export your current work if you want to save it."
        confirmText="Start New Canvas"
        cancelText="Keep Current Canvas"
        type="danger"
      />
    </>
  );
};