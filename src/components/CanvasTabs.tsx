import React from 'react';
import { X } from 'lucide-react';
import { Canvas } from '../types';

interface CanvasTabsProps {
  canvases: Record<string, Canvas>;
  activeCanvasId: string;
  onSwitchCanvas: (canvasId: string) => void;
  onDeleteCanvas: (canvasId: string) => void;
  isSidebarCollapsed: boolean;
}

export const CanvasTabs: React.FC<CanvasTabsProps> = ({
  canvases,
  activeCanvasId,
  onSwitchCanvas,
  onDeleteCanvas,
  isSidebarCollapsed,
}) => {
  const canvasList = Object.values(canvases).sort((a, b) => a.createdAt - b.createdAt);
  const canDeleteCanvas = Object.keys(canvases).length > 1;

  const handleDeleteCanvas = (e: React.MouseEvent, canvasId: string) => {
    e.stopPropagation();
    if (canDeleteCanvas) {
      if (confirm('Are you sure you want to delete this canvas?')) {
        onDeleteCanvas(canvasId);
      }
    }
  };

  return (
    <div 
      className={`fixed top-0 left-0 right-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm border-b border-gray-200/50 dark:border-gray-700/50 z-30 transition-all duration-300 ${
        isSidebarCollapsed ? 'pl-12' : 'pl-64'
      }`}
    >
      <div className="flex items-center overflow-x-auto scrollbar-hide">
        {canvasList.map((canvas) => (
          <div
            key={canvas.id}
            className={`group flex items-center gap-2 px-4 py-3 cursor-pointer border-r border-gray-200/50 dark:border-gray-700/50 min-w-0 flex-shrink-0 transition-colors ${
              canvas.id === activeCanvasId
                ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800/50 text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => onSwitchCanvas(canvas.id)}
          >
            <span className="text-sm font-medium truncate max-w-32">
              {canvas.name}
            </span>
            {canDeleteCanvas && (
              <button
                onClick={(e) => handleDeleteCanvas(e, canvas.id)}
                className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-opacity"
                title="Delete canvas"
              >
                <X size={12} />
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
